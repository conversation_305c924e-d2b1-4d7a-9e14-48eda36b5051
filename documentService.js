
import * as dom from './domElements.js';
import * as state from './state.js';
import * as ui from './ui.js';

export function selectDocument(docId) {
    state.setCurrentDocId(docId);
    state.setCurrentDocumentFile(state.docFiles.find(df => df.id === docId));
    ui.renderDocList();
    loadAndDisplayDocument();
    ui.updateAudioControlsUI(); // For TTS button status
}

export function loadAndDisplayDocument() {
    if (!state.currentDocumentFile) {
        dom.docContentHeading.textContent = 'Content: None Selected';
        dom.docPlaceholder.style.display = 'block';
        dom.docEditorTextarea.style.display = 'none';
        dom.docHtmlViewer.style.display = 'none';
        dom.docEditorTextarea.value = '';
        dom.docHtmlViewer.innerHTML = '';
        state.setIsDocEditing(false);
        ui.updateEditModeUI();
        return;
    }

    dom.docContentHeading.textContent = `Content: ${state.currentDocumentFile.name}`;
    dom.docPlaceholder.style.display = 'none';
    const reader = new FileReader();

    if (state.currentDocumentFile.type === 'txt') {
        reader.onload = (e) => {
            const content = e.target.result;
            dom.docEditorTextarea.value = content;
            if (state.isDocEditing) {
                dom.docEditorTextarea.style.display = 'block';
                dom.docHtmlViewer.style.display = 'none';
            } else {
                dom.docHtmlViewer.innerHTML = `<pre>${content.replace(/</g, "&lt;").replace(/>/g, "&gt;")}</pre>`;
                dom.docHtmlViewer.style.display = 'block';
                dom.docEditorTextarea.style.display = 'none';
            }
            // Reset edit mode on new doc load, unless it was already editing this doc
            // This behavior might need adjustment based on desired UX. For now, reset.
            state.setIsDocEditing(false); 
            ui.updateEditModeUI();
        };
        reader.readAsText(state.currentDocumentFile.file);
    } else if (state.currentDocumentFile.type === 'docx') {
        reader.onload = (e) => {
            if (window.mammoth) {
                window.mammoth.convertToHtml({ arrayBuffer: e.target.result })
                    .then(result => {
                        dom.docHtmlViewer.innerHTML = result.value;
                        dom.docHtmlViewer.style.display = 'block';
                        dom.docEditorTextarea.style.display = 'none';
                        dom.docEditorTextarea.value = ''; // Clear textarea if docx is loaded
                    })
                    .catch(err => {
                        console.error('Error converting DOCX:', err);
                        dom.docHtmlViewer.innerHTML = `<p>Error loading DOCX: ${err.message}</p>`;
                        dom.docHtmlViewer.style.display = 'block';
                        dom.docEditorTextarea.style.display = 'none';
                    });
            } else {
                dom.docHtmlViewer.innerHTML = '<p>Mammoth.js library not loaded. Cannot display .docx.</p>';
                dom.docHtmlViewer.style.display = 'block';
                dom.docEditorTextarea.style.display = 'none';
            }
            state.setIsDocEditing(false); // DOCX is not editable here
            ui.updateEditModeUI();
        };
        reader.readAsArrayBuffer(state.currentDocumentFile.file);
    }
}

export function toggleEditMode() {
    if (!state.currentDocumentFile || state.currentDocumentFile.type !== 'txt') {
        ui.updateStatus('Editing is only supported for .txt files.');
        return;
    }
    state.setIsDocEditing(!state.isDocEditing);
    ui.updateStatus(state.isDocEditing ? 'Edit mode enabled.' : 'View mode enabled.');
    ui.updateEditModeUI();
}

export function insertBreakTag(duration) {
    if (!state.isDocEditing || !state.currentDocumentFile || state.currentDocumentFile.type !== 'txt' || !dom.docEditorTextarea) {
        ui.updateStatus('Enable edit mode for a .txt file to insert break tags.');
        return;
    }
    const textarea = dom.docEditorTextarea;
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const text = textarea.value;
    const breakTag = `<break time="${duration}s"/>`;
    const newText = text.substring(0, start) + breakTag + text.substring(end);
    textarea.value = newText;
    setTimeout(() => {
        textarea.focus();
        textarea.selectionStart = textarea.selectionEnd = start + breakTag.length;
    }, 0);
}

export function saveDocument() {
    if (!state.currentDocumentFile || state.currentDocumentFile.type !== 'txt' || !state.isDocEditing) {
        ui.updateStatus('Only .txt files in edit mode can be saved.');
        return;
    }
    const blob = new Blob([dom.docEditorTextarea.value], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = state.currentDocumentFile.name; // Use original name for download
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    ui.updateStatus(`Document ${state.currentDocumentFile.name} prepared for download.`);
}

export function findAndSetCorrespondingDocument(trackId) {
    const audioFile = state.musicFiles.find(mf => mf.id === trackId);
    if (!audioFile) {
        state.setCurrentDocId(null);
        state.setCurrentDocumentFile(null);
        loadAndDisplayDocument(); 
        return;
    }
    // Remove potential TTS prefixes like (G-TTS) or (MS-TTS) and the extension
    const audioBaseName = audioFile.name
        .replace(/^\((G-TTS|MS-TTS)\)\s*/i, '') // Remove (G-TTS) or (MS-TTS) prefix
        .substring(0, audioFile.name.replace(/^\((G-TTS|MS-TTS)\)\s*/i, '').lastIndexOf('.'))
        .toLowerCase();

    const foundDoc = state.docFiles.find(df =>
        df.name.substring(0, df.name.lastIndexOf('.')).toLowerCase() === audioBaseName
    );

    if (foundDoc) {
        if (state.currentDocId !== foundDoc.id) { // Only select if it's a different document
            selectDocument(foundDoc.id);
        }
    } else {
        // If no corresponding document is found, clear the current document display
        // only if there was a document previously selected or if we want to ensure no stale doc is shown.
        if (state.currentDocId !== null) {
            state.setCurrentDocId(null);
            state.setCurrentDocumentFile(null);
            loadAndDisplayDocument();
            ui.updateStatus(`No corresponding document found for ${audioFile.name}.`);
        }
    }
}
