import * as state from './state.js';

let availableThemes = {};

// Define a fallback theme in case the import fails, ensuring the app doesn't crash.
const fallbackTheme = {
    'default': {
        name: 'Default Light (Fallback)',
        properties: {
            '--bg-primary': '#f4f7f9',
            '--bg-secondary': '#ffffff',
            '--text-primary': '#333',
            '--bg-header': '#34495e',
            '--text-header': '#ffffff',
            '--button-primary-bg': '#5dade2',
            '--text-button-primary': '#ffffff',
            '--border-primary': '#dde4e9',
            '--bg-panel': '#fdfdfd',
        }
    }
};

export async function initThemes() {
    try {
        // Use dynamic import to prevent crashing the app if the file is missing
        const themeModule = await import('./themes/index.js');
        availableThemes = themeModule.themes;
        console.log("Successfully loaded themes from ./themes/index.js");
    } catch (error) {
        console.error("CRITICAL: Could not load themes from './themes/index.js'. This file might be missing or in the wrong directory. Using a fallback theme.", error);
        availableThemes = fallbackTheme;
    }
    // Ensure there's always at least a default theme
    if (!availableThemes['default']) {
        availableThemes = { ...availableThemes, ...fallbackTheme };
    }
    state.setCurrentThemeName('default');
}

export function applyTheme(themeName) {
    const theme = availableThemes[themeName];
    if (!theme) {
        console.warn(`Theme "${themeName}" not found. Applying default theme.`);
        applyTheme('default'); // Fallback to default
        return;
    }

    if (!theme.properties || typeof theme.properties !== 'object') {
        console.error(`Theme "${themeName}" has invalid properties. Cannot apply theme.`);
        return;
    }

    const root = document.documentElement;
    
    Object.keys(theme.properties).forEach(property => {
        root.style.setProperty(property, theme.properties[property]);
    });
    
    console.log(`[Theme Service] Applied theme: ${theme.name} (key: ${themeName}).`);
    state.setCurrentThemeName(themeName);
}

export function getAvailableThemes() {
    return availableThemes;
}

export function getCurrentTheme() {
    return availableThemes[state.currentThemeName];
}