
import * as dom from './domElements.js';
import * as state from './state.js';
import * as ttsService from './ttsService.js';
import { G_TTS_API_KEY, MS_TTS_API_KEY, MS_TTS_SERVICE_REGION, AI_VOICE_CREATOR_ENCODINGS, DEFAULT_G_TTS_VOICE_NAME, DEFAULT_MS_TTS_VOICE_NAME, SUPPORTED_DOC_EXTENSIONS } from './constants.js';
import { downloadBlob, ssmlEscapeXmlEntities, naturalSort } from './utils.js';

const SSML_BREAKPOINT_MARKER = "<!--BREAKPOINT-->";
let currentLoadedFile = null; // For single file mode
let batchFilesToProcess = []; // For folder mode
let currentProcessingMode = 'single'; // 'single' or 'folder'
let currentTTSProvider = 'google'; // 'google' or 'microsoft'

// Hold references to UI functions passed from index.js
let uiUpdateStatusMain = () => {}; // For the main status bar
let uiPopulateGoogleVoiceSelectorFunction = () => {};
let uiPopulateMicrosoftVoiceSelectorFunction = () => {};


function logStatus(message, type = 'info') {
    if (!dom.aiVoiceStatusLog) return;
    const p = document.createElement('p');
    p.textContent = message;
    if (type === 'error') p.classList.add('error');
    if (type === 'success') p.classList.add('success');
    
    const maxLogEntries = 100; 
    while (dom.aiVoiceStatusLog.childNodes.length >= maxLogEntries) {
        dom.aiVoiceStatusLog.removeChild(dom.aiVoiceStatusLog.firstChild);
    }
    dom.aiVoiceStatusLog.appendChild(p);
    dom.aiVoiceStatusLog.scrollTop = dom.aiVoiceStatusLog.scrollHeight; // Auto-scroll
}

function updateUIVisibilityForMode(mode) {
    currentProcessingMode = mode;
    if (mode === 'single') {
        dom.aiVoiceSingleInputControls.style.display = 'block';
        dom.aiVoiceFolderInputControls.style.display = 'none';
        dom.aiVoiceTextareaContainer.style.display = 'flex'; 
        if (currentLoadedFile) {
            dom.aiVoiceSSMLLoadedFileInfo.textContent = `File: ${currentLoadedFile.name}`;
        } else if (dom.aiVoiceSSMLTextarea.value.trim() !== "") {
            dom.aiVoiceSSMLLoadedFileInfo.textContent = `Using content from SSML Editor or pasted input.`;
        } else {
            dom.aiVoiceSSMLLoadedFileInfo.textContent = `No SSML loaded for single input.`;
        }
    } else { // folder mode
        dom.aiVoiceSingleInputControls.style.display = 'none';
        dom.aiVoiceFolderInputControls.style.display = 'block';
        dom.aiVoiceTextareaContainer.style.display = 'none'; 
        if (batchFilesToProcess.length > 0) {
            dom.aiVoiceSSMLLoadedFileInfo.textContent = `${batchFilesToProcess.length} file(s) selected for batch processing.`;
        } else {
            dom.aiVoiceSSMLLoadedFileInfo.textContent = `No folder selected for batch processing.`;
        }
    }
    if (mode === 'single') {
        batchFilesToProcess = [];
    } else {
        currentLoadedFile = null;
    }
}

async function readFileContent(fileObject) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = async (e) => {
            let rawContent = "";
            try {
                if (fileObject.name.endsWith('.txt')) {
                    rawContent = e.target.result;
                } else if (fileObject.name.endsWith('.docx')) {
                    if (window.mammoth) {
                        const result = await window.mammoth.extractRawText({ arrayBuffer: e.target.result });
                        rawContent = result.value;
                    } else {
                        throw new Error("Mammoth.js library not available for DOCX processing.");
                    }
                }
                resolve(rawContent);
            } catch (err) {
                reject(err);
            }
        };
        reader.onerror = () => {
            reject(new Error(`Error reading file: ${fileObject.name}`));
        };

        if (fileObject.name.endsWith('.txt')) {
            reader.readAsText(fileObject);
        } else if (fileObject.name.endsWith('.docx')) {
            reader.readAsArrayBuffer(fileObject);
        } else {
            reject(new Error(`Unsupported file type for reading content: ${fileObject.name}`));
        }
    });
}


async function handleSSMLFileUpload(event) { 
    const file = event.target.files[0];
    if (!file) return;

    currentLoadedFile = file; 
    batchFilesToProcess = []; 
    updateUIVisibilityForMode('single'); 

    dom.aiVoiceSSMLLoadedFileInfo.textContent = `File: ${file.name}`;
    logStatus(`Loading SSML from "${file.name}"...`);

    const extension = file.name.substring(file.name.lastIndexOf('.')).toLowerCase();
    if (!SUPPORTED_DOC_EXTENSIONS.includes(extension)) {
        logStatus(`Unsupported file type: ${extension}. Please select .txt or .docx.`, 'error');
        dom.aiVoiceSSMLFileInput.value = ''; 
        dom.aiVoiceSSMLLoadedFileInfo.textContent = `Error: Unsupported file type.`;
        currentLoadedFile = null;
        return;
    }
    
    try {
        const rawContent = await readFileContent(file);
        dom.aiVoiceSSMLTextarea.value = rawContent;
        logStatus(`Successfully loaded SSML from "${file.name}".`);
    } catch (err) {
        console.error('Error processing SSML file:', err);
        logStatus(`Error loading SSML: ${err.message}`, 'error');
        dom.aiVoiceSSMLLoadedFileInfo.textContent = `Error loading: ${file.name}`;
        currentLoadedFile = null;
    }
    dom.aiVoiceSSMLFileInput.value = ''; 
}

async function handleSSMLFolderUpload(event) {
    const files = event.target.files;
    if (!files || files.length === 0) {
        logStatus("No folder or files selected.", "error");
        return;
    }

    currentLoadedFile = null; 
    dom.aiVoiceSSMLTextarea.value = ""; 

    const validFiles = Array.from(files).filter(file => {
        const extension = file.name.substring(file.name.lastIndexOf('.')).toLowerCase();
        return SUPPORTED_DOC_EXTENSIONS.includes(extension);
    });

    if (validFiles.length === 0) {
        logStatus("No supported .txt or .docx files found in the selected folder.", "error");
        dom.aiVoiceSSMLLoadedFileInfo.textContent = "No compatible files in folder.";
        batchFilesToProcess = [];
        updateUIVisibilityForMode('folder');
        return;
    }

    validFiles.sort((a, b) => naturalSort(a.name, b.name));
    
    batchFilesToProcess = validFiles;
    logStatus(`Loaded ${batchFilesToProcess.length} file(s) for batch processing.`);
    updateUIVisibilityForMode('folder'); 

    dom.aiVoiceSSMLFolderInput.value = ''; 
}


function handleLoadFromEditor() {
    if (currentProcessingMode !== 'single') {
        logStatus("Switch to 'Single Input' mode to load from SSML Editor.", "error");
        return;
    }
    if (dom.ssmlTextWidget && dom.aiVoiceSSMLTextarea) {
        const editorContent = dom.ssmlTextWidget.value;
        if (editorContent.trim() === "") {
            logStatus("SSML Editor is empty. Nothing to load.", "error");
            return;
        }
        dom.aiVoiceSSMLTextarea.value = editorContent;
        currentLoadedFile = state.ssmlEditorFile; 
        batchFilesToProcess = [];
        if (currentLoadedFile) {
             dom.aiVoiceSSMLLoadedFileInfo.textContent = `From Editor: ${currentLoadedFile.name}`;
        } else {
            dom.aiVoiceSSMLLoadedFileInfo.textContent = `From Editor (unsaved content)`;
        }
        logStatus("Loaded content from SSML Editor.");
    } else {
        logStatus("Could not access SSML Editor content.", "error");
    }
}


function prepareSSMLForSynthesis(rawTextChunk, rate) {
    let ssmlContent = rawTextChunk.trim();
    const hasSpeakTag = /<speak[\s>]/.test(ssmlContent);

    if (!hasSpeakTag) {
        ssmlContent = ssmlEscapeXmlEntities(ssmlContent);
        ssmlContent = `<speak><prosody rate="${rate}">${ssmlContent}</prosody></speak>`;
    } else {
        if (rate !== 1.0) { // Only attempt to inject/modify prosody if rate is not default
            const prosodyRegex = /<prosody[^>]*rate="([^"]*)"[^>]*>/i;
            const speakContentRegex = /(<speak[^>]*>)([\s\S]*?)(<\/speak>)/i;
            let match = ssmlContent.match(speakContentRegex);

            if (match && match[2]) { // If we have content within <speak> tags
                let innerContent = match[2];
                if (prosodyRegex.test(innerContent)) { // If prosody tag with rate exists inside
                    innerContent = innerContent.replace(prosodyRegex, (prosodyMatch, oldRate) => {
                        return prosodyMatch.replace(`rate="${oldRate}"`, `rate="${rate}"`);
                    });
                } else { // No prosody tag with rate, wrap inner content
                    innerContent = `<prosody rate="${rate}">${innerContent}</prosody>`;
                }
                ssmlContent = `${match[1]}${innerContent}${match[3]}`;
            } else { // Fallback: if <speak> tags are present but structure is unusual or content is empty
                 ssmlContent = `<speak><prosody rate="${rate}">${ssmlContent.replace(/<speak[^>]*>|<\/speak>/gi, '')}</prosody></speak>`;
            }
        }
    }
    return ssmlContent;
}

function updateSynthesizeButtonState() {
    let disabled = true;
    if (currentTTSProvider === 'google') {
        disabled = !G_TTS_API_KEY;
    } else if (currentTTSProvider === 'microsoft') {
        disabled = !MS_TTS_API_KEY || !MS_TTS_SERVICE_REGION;
    }
    dom.aiVoiceSynthesizeBtn.disabled = disabled;
    if (disabled) {
        logStatus(`"${currentTTSProvider === 'google' ? 'Google' : 'Microsoft'} TTS" provider selected, but API key/config is missing. Generation disabled.`, 'error');
    }
}

function handleProviderChange() {
    currentTTSProvider = dom.aiVoiceTTSProviderSelect.value;
    logStatus(`Switched to ${currentTTSProvider === 'google' ? 'Google' : 'Microsoft'} TTS provider.`);
    if (currentTTSProvider === 'google') {
        uiPopulateGoogleVoiceSelectorFunction(dom.aiVoiceTTSVoiceSelect, state.fetchedGTTSVoices, DEFAULT_G_TTS_VOICE_NAME);
    } else if (currentTTSProvider === 'microsoft') {
        uiPopulateMicrosoftVoiceSelectorFunction(dom.aiVoiceTTSVoiceSelect, state.fetchedMsTTSVoices, DEFAULT_MS_TTS_VOICE_NAME);
    }
    updateSynthesizeButtonState();
}


async function handleSynthesizeAudio() {
    updateSynthesizeButtonState(); // Re-check before proceeding
    if (dom.aiVoiceSynthesizeBtn.disabled) {
        logStatus("Cannot synthesize. TTS Provider not correctly configured or API key missing.", "error");
        return;
    }

    const selectedVoiceName = dom.aiVoiceTTSVoiceSelect.value;
    let voiceDetails;
    if (currentTTSProvider === 'google') {
        voiceDetails = state.fetchedGTTSVoices.find(v => v.name === selectedVoiceName);
    } else { // microsoft
        voiceDetails = state.fetchedMsTTSVoices.find(v => v.name === selectedVoiceName);
    }

    if (!voiceDetails) {
        logStatus(`Invalid ${currentTTSProvider} Voice selected.`, "error");
        return;
    }

    const speakingRate = parseFloat(dom.aiVoiceSpeedSlider.value);
    const encodingKey = dom.aiVoiceEncodingSelect.value; 
    const encodingConfig = AI_VOICE_CREATOR_ENCODINGS[encodingKey];
    if (!encodingConfig) {
        logStatus("Invalid audio format selected.", "error");
        return;
    }
    
    const userDefinedPrefix = dom.aiVoiceFilenamePrefix.value.trim();
    dom.aiVoiceSynthesizeBtn.disabled = true; // Disable during processing
    logStatus(`Starting audio generation process using ${currentTTSProvider}...`);

    if (currentProcessingMode === 'single') {
        const ssmlInput = dom.aiVoiceSSMLTextarea.value.trim();
        if (!ssmlInput) {
            logStatus("SSML input is empty for single processing.", "error");
            updateSynthesizeButtonState(); // Re-enable button
            return;
        }
        let baseFilename = userDefinedPrefix || (currentLoadedFile ? currentLoadedFile.name.replace(/\.[^/.]+$/, "") : 'generated_audio');
        baseFilename = baseFilename.replace(/\s+/g, '_').replace(/[^a-zA-Z0-9_.-]/g, '');
        
        await processSingleContent(ssmlInput, baseFilename, voiceDetails, speakingRate, encodingConfig, currentTTSProvider);

    } else if (currentProcessingMode === 'folder') {
        if (batchFilesToProcess.length === 0) {
            logStatus("No files selected for batch processing.", "error");
            updateSynthesizeButtonState(); // Re-enable button
            return;
        }
        for (const file of batchFilesToProcess) {
            logStatus(`Processing file: ${file.name} with ${currentTTSProvider}...`);
            try {
                const fileContent = await readFileContent(file);
                if (!fileContent.trim()) {
                    logStatus(`Skipping empty file: ${file.name}.`);
                    continue;
                }
                let baseFilenameForFile = file.name.replace(/\.[^/.]+$/, ""); 
                if (userDefinedPrefix) {
                    baseFilenameForFile = `${userDefinedPrefix}_${baseFilenameForFile}`;
                }
                baseFilenameForFile = baseFilenameForFile.replace(/\s+/g, '_').replace(/[^a-zA-Z0-9_.-]/g, '');

                await processSingleContent(fileContent, baseFilenameForFile, voiceDetails, speakingRate, encodingConfig, currentTTSProvider);

            } catch (error) {
                console.error(`Error processing file ${file.name} in batch:`, error);
                logStatus(`Error for file ${file.name}: ${error.message}`, 'error');
            }
        }
    }

    logStatus("Audio generation process complete.");
    updateSynthesizeButtonState(); // Re-enable button based on current config
}

async function processSingleContent(ssmlContent, baseFilename, voiceDetails, speakingRate, encodingConfig, provider) {
    const ssmlChunks = ssmlContent.split(SSML_BREAKPOINT_MARKER).map(s => s.trim()).filter(s => s.length > 0);
    
    if (ssmlChunks.length === 0 && ssmlContent.length > 0) { 
        ssmlChunks.push(ssmlContent);
    }
    if (ssmlChunks.length === 0) {
        logStatus(`No content to process for ${baseFilename} using ${provider}.`, 'error');
        return;
    }

    for (let i = 0; i < ssmlChunks.length; i++) {
        const chunk = ssmlChunks[i];
        const partNumberSuffix = ssmlChunks.length > 1 ? `_part_${i + 1}` : '';
        const filename = `${baseFilename}${partNumberSuffix}${encodingConfig.extension}`;
        
        logStatus(`Synthesizing chunk ${i + 1}/${ssmlChunks.length} for "${filename}" using ${provider}...`);

        try {
            const fullSsmlToSynthesize = prepareSSMLForSynthesis(chunk, speakingRate);
            
            const synthesizedAudioData = await ttsService.synthesizeTextToAudio(
                fullSsmlToSynthesize,
                voiceDetails.name,
                provider, 
                baseFilename + partNumberSuffix, 
                encodingConfig.extension,
                encodingConfig.apiValue, 
                encodingConfig.mimeType  
            );

            downloadBlob(synthesizedAudioData.file, filename); 
            logStatus(`Successfully generated and downloaded "${filename}" using ${provider}.`, "success");

        } catch (error) {
            console.error(`Error synthesizing chunk ${i + 1} ("${filename}") with ${provider}:`, error);
            logStatus(`Error for chunk ${i + 1} ("${filename}") with ${provider}: ${error.message}`, 'error');
        }
    }
}


export function initAiVoiceCreator(uiHelpers) {
    uiUpdateStatusMain = uiHelpers.updateStatus;
    uiPopulateGoogleVoiceSelectorFunction = uiHelpers.populateGoogleVoiceSelector;
    uiPopulateMicrosoftVoiceSelectorFunction = uiHelpers.populateMicrosoftVoiceSelector;

    // Populate encoding selector
    if (uiHelpers.populateEncodingSelector && dom.aiVoiceEncodingSelect) {
        uiHelpers.populateEncodingSelector(dom.aiVoiceEncodingSelect, 'mp3');
    }

    if (!dom.aiVoiceCreatorBtn) {
        console.warn("AI Voice Creator DOM elements not fully initialized.");
        return;
    }
    
    if (dom.aiVoiceModeSingleRadio) {
        dom.aiVoiceModeSingleRadio.addEventListener('change', () => updateUIVisibilityForMode('single'));
    }
    if (dom.aiVoiceModeFolderRadio) {
        dom.aiVoiceModeFolderRadio.addEventListener('change', () => updateUIVisibilityForMode('folder'));
    }
    updateUIVisibilityForMode(dom.aiVoiceModeSingleRadio.checked ? 'single' : 'folder');

    if (dom.aiVoiceTTSProviderSelect) {
        dom.aiVoiceTTSProviderSelect.addEventListener('change', handleProviderChange);
        // Initial population based on default provider
        currentTTSProvider = dom.aiVoiceTTSProviderSelect.value; 
    }
    
    // Initial voice population:
    if (currentTTSProvider === 'google') {
        uiPopulateGoogleVoiceSelectorFunction(dom.aiVoiceTTSVoiceSelect, state.fetchedGTTSVoices, DEFAULT_G_TTS_VOICE_NAME);
    } else if (currentTTSProvider === 'microsoft') {
         uiPopulateMicrosoftVoiceSelectorFunction(dom.aiVoiceTTSVoiceSelect, state.fetchedMsTTSVoices, DEFAULT_MS_TTS_VOICE_NAME);
    }


    if (dom.aiVoiceSSMLFileInput) {
        dom.aiVoiceSSMLFileInput.addEventListener('change', handleSSMLFileUpload);
    }
    if (dom.aiVoiceSSMLFolderInput) {
        dom.aiVoiceSSMLFolderInput.addEventListener('change', handleSSMLFolderUpload);
    }
    if (dom.aiVoiceLoadFromEditorBtn) {
        dom.aiVoiceLoadFromEditorBtn.addEventListener('click', handleLoadFromEditor);
    }

    if (dom.aiVoiceSpeedSlider && dom.aiVoiceSpeedValueDisplay) {
        dom.aiVoiceSpeedSlider.addEventListener('input', (e) => {
            dom.aiVoiceSpeedValueDisplay.textContent = parseFloat(e.target.value).toFixed(2);
        });
        dom.aiVoiceSpeedValueDisplay.textContent = parseFloat(dom.aiVoiceSpeedSlider.value).toFixed(2);
    }

    if (dom.aiVoiceSynthesizeBtn) {
        dom.aiVoiceSynthesizeBtn.addEventListener('click', handleSynthesizeAudio);
    }
    updateSynthesizeButtonState(); // Set initial state of synthesize button
    logStatus("AI Voice Creator initialized. Ready.");
}