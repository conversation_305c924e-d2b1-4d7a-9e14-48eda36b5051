
// --- Utility Functions ---
export const naturalSort = (aName, bName) => {
    const re = /(\d+)/g;
    const aname = aName.toLowerCase();
    const bname = bName.toLowerCase();
    const as = aname.split(re);
    const bs = bname.split(re);
    for (let i = 0; i < Math.min(as.length, bs.length); i++) {
        const x = as[i];
        const y = bs[i];
        if (x !== y) {
            const nx = parseInt(x, 10);
            const ny = parseInt(y, 10);
            if (!isNaN(nx) && !isNaN(ny)) return nx - ny;
            return x.localeCompare(y);
        }
    }
    return as.length - bs.length;
};

export const formatTime = (seconds) => {
    if (isNaN(seconds) || seconds < 0) return '0:00';
    const minutes = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${minutes}:${secs < 10 ? '0' : ''}${secs}`;
};

export function base64ToBlob(base64, type = 'application/octet-stream') {
    try {
        const byteCharacters = atob(base64);
        const byteNumbers = new Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);
        return new Blob([byteArray], { type });
    } catch (e) {
        console.error("Error in base64ToBlob:", e);
        // Consider using a more generic way to update status or throw error
        // updateStatus("Error decoding TTS audio data."); 
        return null;
    }
}

export function ssmlEscapeXmlEntities(text) {
    if (typeof text !== 'string') return '';
    return text.replace(/&/g, '&amp;')
               .replace(/</g, '&lt;')
               .replace(/>/g, '&gt;')
               .replace(/"/g, '&quot;')
               .replace(/'/g, '&apos;');
}

export function downloadTextFile(text, filename) {
    const blob = new Blob([text], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

export function downloadBlob(blob, filename) {
    if (!blob || !filename) {
        console.error('downloadBlob: Invalid blob or filename');
        return;
    }

    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.style.display = 'none';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}
